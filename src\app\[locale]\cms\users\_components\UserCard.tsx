import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { User, Mail, Calendar, Shield } from "lucide-react";
import Text from "@/components/Text";
import { Badge } from "@/components/ui/badge";
import { UserType } from "@/app/[locale]/(auth)/model";
import { Separator } from "@/components/ui/separator";
import { getTranslations } from "next-intl/server";
import { ReactNode } from "react";
import UserCardActions from "./UserCardActions";

const statusColors = {
  active: "bg-green-100 text-green-800",
  banned: "bg-red-100 text-red-800",
} as const;

const roleColors = {
  admin: "bg-blue-100 text-blue-800",
  user: "bg-gray-100 text-gray-800",
} as const;

type UserCardProps = {
  user: UserType;
  children?: ReactNode;
  isAdmin?: boolean;
};

export default async function UserCard({ user, isAdmin = true }: UserCardProps) {
  const t = await getTranslations("CMS.users");
  const tUser = await getTranslations("User");

  const userStatus = user.banned ? "banned" : "active";
  const statusText = user.banned ? t("table.statusBanned") : t("table.statusActive");

  return (
    <Card className="relative gap-2">
      <CardHeader>
        <div className="flex flex-wrap items-center justify-between gap-2">
          <div className="min-w-0 flex-1">
            <CardTitle className="text-base text-wrap">
              {user.firstName} {user.lastName}
            </CardTitle>
          </div>
          <div className="flex items-center gap-2">
            <Badge
              className={cn(
                "text-xs font-semibold",
                statusColors[userStatus],
              )}
            >
              {statusText}
            </Badge>
            {isAdmin && <UserCardActions user={user} />}
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex flex-col gap-4">
        <article className="flex flex-wrap gap-4 text-sm">
          <div className="flex items-center gap-2">
            <Mail className="text-muted-foreground size-4" />
            <span className="truncate">{user.email}</span>
          </div>
          <div className="flex items-center gap-2">
            <Shield className="text-muted-foreground size-4" />
            <Badge
              variant={user.role === "admin" ? "default" : "secondary"}
              className={cn(
                "text-xs",
                roleColors[user.role],
              )}
            >
              {user.role}
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            <Calendar className="text-muted-foreground size-4" />
            <span>{new Date(user.createdAt).toLocaleDateString()}</span>
          </div>
        </article>
        <Separator />
        <article className="flex items-baseline justify-between">
          <Text as="span" size="sm" className="text-muted-foreground">
            {t("table.userId")}:
          </Text>
          <Text as="span" size="sm" className="font-mono text-xs">
            {user.id}
          </Text>
        </article>
      </CardContent>
    </Card>
  );
}
