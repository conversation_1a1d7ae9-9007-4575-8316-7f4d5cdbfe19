"use client";

import { UserType } from "@/app/[locale]/(auth)/model";
import { buttonVariants } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { cn } from "@/lib/utils";
import { MoreVertical, Shield, ShieldOff, LogOut, Trash2, CircleAlert } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState, useTransition } from "react";
import { toast } from "sonner";
import { banUser, unbanUser, revokeAllUserSessions, removeUser } from "../actions";

type UserCardActionsProps = {
  user: UserType;
};

export default function UserCardActions({ user }: UserCardActionsProps) {
  const t = useTranslations("CMS.users");
  const [isPending, startTransition] = useTransition();
  const [alertDialog, setAlertDialog] = useState<{
    isOpen: boolean;
    action: "ban" | "unban" | "revoke" | "delete" | null;
    title: string;
    description: string;
  }>({
    isOpen: false,
    action: null,
    title: "",
    description: "",
  });

  const handleToggleStatus = () => {
    const action = user.banned ? "unban" : "ban";
    const actionText = user.banned ? t("actions.unban") : t("actions.ban");
    setAlertDialog({
      isOpen: true,
      action,
      title: t("actions.confirmTitle", { action: actionText }),
      description: t("actions.confirmDescription", {
        action: actionText.toLowerCase(),
        userName: `${user.firstName} ${user.lastName}`,
      }),
    });
  };

  const handleRevokeSession = () => {
    setAlertDialog({
      isOpen: true,
      action: "revoke",
      title: t("actions.confirmTitle", { action: t("actions.revokeSessions") }),
      description: t("actions.confirmRevokeDescription", {
        userName: `${user.firstName} ${user.lastName}`,
      }),
    });
  };

  const handleDelete = () => {
    setAlertDialog({
      isOpen: true,
      action: "delete",
      title: t("actions.confirmTitle", { action: t("actions.remove") }),
      description: t("actions.confirmDeleteDescription", {
        userName: `${user.firstName} ${user.lastName}`,
      }),
    });
  };

  const executeAction = () => {
    if (!alertDialog.action) return;

    startTransition(async () => {
      try {
        let result;
        switch (alertDialog.action) {
          case "ban":
            result = await banUser({ userId: user.id });
            break;
          case "unban":
            result = await unbanUser({ userId: user.id });
            break;
          case "revoke":
            result = await revokeAllUserSessions({ userId: user.id });
            break;
          case "delete":
            result = await removeUser({ userId: user.id });
            break;
          default:
            return;
        }

        if (result.success) {
          toast.success(result.message);
        } else {
          toast.error(result.message);
        }
      } catch (error) {
        toast.error(t("actions.genericError"));
      } finally {
        setAlertDialog((prev) => ({ ...prev, isOpen: false }));
      }
    });
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger
          className={cn(
            buttonVariants({ variant: "ghost", size: "sm" }),
            "h-8 w-8 p-0",
          )}
          disabled={isPending}
        >
          <MoreVertical className="h-4 w-4" />
          <span className="sr-only">{t("table.actions")}</span>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={handleToggleStatus} disabled={isPending}>
            {user.banned ? (
              <Shield className="mr-2 h-4 w-4" />
            ) : (
              <ShieldOff className="mr-2 h-4 w-4" />
            )}
            {user.banned ? t("actions.unban") : t("actions.ban")}
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleRevokeSession} disabled={isPending}>
            <LogOut className="mr-2 h-4 w-4" />
            {t("actions.revokeSessions")}
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={handleDelete}
            disabled={isPending}
            className="text-destructive focus:text-destructive"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            {t("actions.remove")}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <AlertDialog
        open={alertDialog.isOpen}
        onOpenChange={(open) =>
          setAlertDialog((prev) => ({ ...prev, isOpen: open }))
        }
      >
        <AlertDialogContent>
          <div className="flex flex-col gap-2 max-sm:items-center sm:flex-row sm:gap-4">
            <div
              className="flex size-9 shrink-0 items-center justify-center rounded-full border"
              aria-hidden="true"
            >
              <CircleAlert className="opacity-80" size={16} />
            </div>
            <AlertDialogHeader>
              <AlertDialogTitle>{alertDialog.title}</AlertDialogTitle>
              <AlertDialogDescription>
                {alertDialog.description}
              </AlertDialogDescription>
            </AlertDialogHeader>
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isPending}>
              {t("actions.cancel")}
            </AlertDialogCancel>
            <AlertDialogAction onClick={executeAction} disabled={isPending}>
              {t("actions.confirm")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
